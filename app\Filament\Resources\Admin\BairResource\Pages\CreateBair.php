<?php

namespace App\Filament\Resources\Admin\BairResource\Pages;

use App\Services\UserInfoService;
use App\Models\Orc;
use App\Filament\Resources\Admin\BairResource;
use App\Models\Bair;
use App\Models\Korpus;
use Filament\Resources\Pages\CreateRecord;

class CreateBair extends CreateRecord
{
    protected static string $resource = BairResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $service = resolve(UserInfoService::class);
        $sukh = $service->getAUSukh();
        $data[Bair::SUKH_ID] = $sukh->id;
        return parent::mutateFormDataBeforeCreate($data);
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
