<?php

namespace App\Filament\Resources\Admin;

use App\Filament\Resources\Admin\DavharResource\Pages;
use App\Filament\Resources\Admin\DavharResource\RelationManagers;
use App\Models\Davhar;
use App\Services\UserInfoService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;


class DavharResource extends Resource
{
    protected static ?string $model = Davhar::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-storefront';
    protected static ?string $navigationLabel = 'Давхарууд';
    protected static ?string $pluralModelLabel = 'Давхарууд';
    protected static ?string $modelLabel = 'давхар';
    protected static ?string $slug = 'davhars';

    // Hide from navigation - only accessible through Orc hierarchy
    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('orc_info')
                            ->label('Орц')
                            ->content(function (?Davhar $record) {
                                if ($record && $record->orc) {
                                    return "{$record->orc->korpus->bair->name} - {$record->orc->korpus->name} - Орц {$record->orc->number}";
                                }

                                $orcId = request()->get('orc_id');
                                if ($orcId) {
                                    $orc = \App\Models\Orc::with('korpus.bair')->find($orcId);
                                    return $orc ? "{$orc->korpus->bair->name} - {$orc->korpus->name} - Орц {$orc->number}" : 'Тодорхойгүй';
                                }

                                return 'Тодорхойгүй';
                            }),

                        Forms\Components\Hidden::make(Davhar::ORC_ID)
                            ->default(function () {
                                return request()->get('orc_id');
                            }),

                        Forms\Components\TextInput::make(Davhar::NUMBER)
                            ->label('Давхарын дугаар')
                            ->required(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->heading('Давхарууд')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Davhar::NUMBER)->label('Давхарын дугаар')->sortable()->searchable(),

                Tables\Columns\TextColumn::make('orc.number')->label('Орц')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('orc.korpus.name')->label('Блок')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('orc.korpus.bair.name')->label('Байр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Davhar::CODE)->label('CV Security код')->sortable(),
                Tables\Columns\TextColumn::make('toots_count')->counts('toots')->label('Тоотын тоо'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\TootsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDavhars::route('/'),
            'create' => Pages\CreateDavhar::route('/create'),
            'edit' => Pages\EditDavhar::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $service = resolve(UserInfoService::class);
        $sukh = $service->getAUSukh();
        return parent::getEloquentQuery()->whereHas('orc.korpus.bair.sukh', function (Builder $query) use($sukh) {
            $query->where('id', $sukh->id);
        });
    }
}
