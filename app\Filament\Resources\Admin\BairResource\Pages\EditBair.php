<?php

namespace App\Filament\Resources\Admin\BairResource\Pages;

use App\Filament\Resources\Admin\BairResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBair extends EditRecord
{
    protected static string $resource = BairResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
