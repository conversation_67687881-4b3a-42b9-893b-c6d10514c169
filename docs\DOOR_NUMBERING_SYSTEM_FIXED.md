# Door Numbering System with Auto-Generation Documentation

## Overview

This document describes the comprehensive door numbering system used in the Smart Door IoT application with enhanced auto-generation capabilities. The system supports multiple numbering schemes to accommodate different building layouts and organizational requirements, with automated generation features at Korpus, Orc, and Davhar levels.

## Hierarchical Structure

The door numbering system follows this hierarchical structure:

```
Sukh (Residents' Committee)
└── Bair (Building) - examples: 57, 105
    └── Korpus (Block) - examples: A, B, 1
        └── Orc (Entrance) - examples: 1, 2
            └── Davhar (Floor) - examples: 4, 12, 16
                └── Toot (Door) - examples: 1, 101, 1106
```

## Auto-Generation System

The system now supports automated generation of hierarchical entities at three levels:

### 1. Korpus Level Auto-Generation

-   **Scope**: Generates all Davhars, and Toots for the entire Korpus
-   **Prerequisites**: Orcs must be created manually before enabling auto-generation
-   **Supported Types**: Type 1 (Korpus-wise), Type 2 (Orc-wise), Type 3 (Floor-wise)

### 2. Orc Level Auto-Generation

-   **Scope**: Generates Davhars and Toots for a specific Orc
-   **Supported Types**: Type 2 (Orc-wise), Type 3 (Floor-wise)
-   **Independence**: Each Orc can have different configurations

### 3. Davhar Level Auto-Generation

-   **Scope**: Generates Toots for a specific Davhar (floor)
-   **Type**: Always uses Type 3 (Floor-wise) logic
-   **Granular Control**: Individual floor-level management

## Numbering Types

### Type 1: Korpus-wise Consecutive Numbering

This numbering system assigns consecutive door numbers across the entire Korpus, with doors distributed across all Orcs and Davhars.

#### Basic Structure

-   **Total doors per Korpus**: Calculated as `Number of Orcs × Davhars per Orc × Doors per Davhar`
-   **Numbering**: Consecutive numbering starting from 1
-   **Distribution**: Doors are distributed sequentially across Orcs and Davhars within each Korpus
-   **Dynamic calculation**: Total doors determined by hierarchical structure, not fixed ranges

#### Example Configuration

**Korpus with 2 Orcs, 16 Davhars per Orc, 6 doors per Davhar:**

```
Total doors = 2 Orcs × 16 Davhars × 6 Doors = 192 doors

Korpus A (192 doors total)
├── Orc 1 (doors 1-96)
│   ├── Davhar 1: doors 1-6
│   ├── Davhar 2: doors 7-12
│   ├── Davhar 3: doors 13-18
│   ├── ...
│   └── Davhar 16: doors 91-96
└── Orc 2 (doors 97-192)
    ├── Davhar 1: doors 97-102
    ├── Davhar 2: doors 103-108
    ├── Davhar 3: doors 109-114
    ├── ...
    └── Davhar 16: doors 187-192
```

#### Calculation Formula

For a Korpus with `N` Orcs, `F` Davhars per Orc, and `D` doors per Davhar:

```
Total doors = N × F × D
Doors per Orc = F × D
Doors per Davhar = D

For Orc i (0-indexed):
  Start door = (i × doors_per_orc) + 1
  End door = (i + 1) × doors_per_orc

For Davhar j within Orc i (0-indexed):
  Start door = (i × doors_per_orc) + (j × doors_per_davhar) + 1
  End door = (i × doors_per_orc) + ((j + 1) × doors_per_davhar)
```

### Type 2: Orc-wise Consecutive Numbering

This numbering system restarts the door numbering from 1 for each Orc within a Korpus. Each Orc has its own independent numbering sequence.

#### Basic Structure

-   **Numbering per Orc**: Each Orc starts from 1
-   **Independence**: Door numbers are independent between Orcs
-   **Distribution**: Doors are distributed across Davhars within each Orc
-   **Dynamic calculation**: Total doors per Orc = `Davhars per Orc × Doors per Davhar`

#### Example Configuration

**Korpus with 3 Orcs, each with different configurations:**

```
Korpus A
├── Orc 1 (8 Davhars × 6 doors = 48 doors: 1-48)
│   ├── Davhar 1: doors 1-6
│   ├── Davhar 2: doors 7-12
│   ├── Davhar 3: doors 13-18
│   ├── Davhar 4: doors 19-24
│   ├── Davhar 5: doors 25-30
│   ├── Davhar 6: doors 31-36
│   ├── Davhar 7: doors 37-42
│   └── Davhar 8: doors 43-48
├── Orc 2 (6 Davhars × 6 doors = 36 doors: 1-36)
│   ├── Davhar 1: doors 1-6
│   ├── Davhar 2: doors 7-12
│   ├── Davhar 3: doors 13-18
│   ├── Davhar 4: doors 19-24
│   ├── Davhar 5: doors 25-30
│   └── Davhar 6: doors 31-36
└── Orc 3 (10 Davhars × 6 doors = 60 doors: 1-60)
    ├── Davhar 1: doors 1-6
    ├── Davhar 2: doors 7-12
    ├── Davhar 3: doors 13-18
    ├── Davhar 4: doors 19-24
    ├── Davhar 5: doors 25-30
    ├── Davhar 6: doors 31-36
    ├── Davhar 7: doors 37-42
    ├── Davhar 8: doors 43-48
    ├── Davhar 9: doors 49-54
    └── Davhar 10: doors 55-60
```

#### Calculation Formula

For an Orc with `F` Davhars and `D` doors per Davhar:

```
Total doors per Orc = F × D

For Davhar j within an Orc (0-indexed):
  Start door = (j × D) + 1
  End door = (j + 1) × D
```

### Type 3: Floor-wise Consecutive Numbering

This numbering system incorporates the floor (Davhar) number into the door number. Each door number consists of the floor number followed by the door sequence within that floor.

#### Basic Structure

-   **Floor-based numbering**: Door numbers include floor number as prefix
-   **Format**: `{floor_number}{door_sequence}` with variable digit lengths
-   **Flexible digits**: Can use 2-digit (11, 12), 3-digit (101, 102), or 4-digit (1001, 1002) formats
-   **Consistency**: Same pattern applies to all Orcs within the Korpus
-   **Sequential doors**: Doors are numbered sequentially within each floor

#### Example Configuration

**Korpus with 2 Orcs, 16 Davhars per Orc, 6 doors per Davhar:**

```
Total doors per Orc = 16 Davhars × 6 doors = 96 doors per Orc

Korpus A
├── Orc 1 (96 doors total)
│   ├── Davhar 1 (Floor 1): doors 101-106
│   ├── Davhar 2 (Floor 2): doors 201-206
│   ├── Davhar 3 (Floor 3): doors 301-306
│   ├── Davhar 4 (Floor 4): doors 401-406
│   ├── Davhar 5 (Floor 5): doors 501-506
│   ├── Davhar 6 (Floor 6): doors 601-606
│   ├── Davhar 7 (Floor 7): doors 701-706
│   ├── Davhar 8 (Floor 8): doors 801-806
│   ├── Davhar 9 (Floor 9): doors 901-906
│   ├── Davhar 10 (Floor 10): doors 1001-1006
│   ├── Davhar 11 (Floor 11): doors 1101-1106
│   ├── Davhar 12 (Floor 12): doors 1201-1206
│   ├── Davhar 13 (Floor 13): doors 1301-1306
│   ├── Davhar 14 (Floor 14): doors 1401-1406
│   ├── Davhar 15 (Floor 15): doors 1501-1506
│   └── Davhar 16 (Floor 16): doors 1601-1606
└── Orc 2 (96 doors total)
    ├── Davhar 1 (Floor 1): doors 101-106
    ├── Davhar 2 (Floor 2): doors 201-206
    ├── Davhar 3 (Floor 3): doors 301-306
    ├── Davhar 4 (Floor 4): doors 401-406
    ├── Davhar 5 (Floor 5): doors 501-506
    ├── Davhar 6 (Floor 6): doors 601-606
    ├── Davhar 7 (Floor 7): doors 701-706
    ├── Davhar 8 (Floor 8): doors 801-806
    ├── Davhar 9 (Floor 9): doors 901-906
    ├── Davhar 10 (Floor 10): doors 1001-1006
    ├── Davhar 11 (Floor 11): doors 1101-1106
    ├── Davhar 12 (Floor 12): doors 1201-1206
    ├── Davhar 13 (Floor 13): doors 1301-1306
    ├── Davhar 14 (Floor 14): doors 1401-1406
    ├── Davhar 15 (Floor 15): doors 1501-1506
    └── Davhar 16 (Floor 16): doors 1601-1606
```

#### Calculation Formula

For a Davhar with floor number `FLOOR_NUM`, `DOORS_PER_FLOOR` doors, and `DIGIT_MULTIPLIER`:

```
For door i within a Davhar (1-indexed):
  Door number = (FLOOR_NUM * DIGIT_MULTIPLIER) + i

Where:
  FLOOR_NUM = Davhar sequence number (1, 2, 3, ...)
  i = Door sequence within the floor (1, 2, 3, ..., DOORS_PER_FLOOR)
  DIGIT_MULTIPLIER = 10 (2-digit), 100 (3-digit), 1000 (4-digit), etc.

Examples:
  2-digit format: Floor 1, Door 1 = (1 * 10) + 1 = 11
  3-digit format: Floor 1, Door 1 = (1 * 100) + 1 = 101
  4-digit format: Floor 1, Door 1 = (1 * 1000) + 1 = 1001
```

## Auto-Generation Features

### Korpus Level Auto-Generation

#### Prerequisites

-   Orcs must be created manually before enabling auto-generation
-   All required Orcs should exist in the system

#### UI Components

-   **auto_generate**: Boolean toggle field visible on KorpusEdit form
-   **Conditional Fields**: When `auto_generate = true`, the following fields become visible:
    -   `numbering_type`: Dropdown (1 = Korpus-wise, 2 = Orc-wise, 3 = Floor-wise)
    -   `number_of_floors`: Integer input for floors per Orc
    -   `doors_per_floor`: Integer input for doors per floor
    -   `digit_multiplier`: Integer input (10, 100, 1000, etc.) - visible only for Type 3
    -   **Generate Button**: Triggers the auto-generation process

#### Behavior Logic

1. **First Time Activation**: When `auto_generate` is toggled to `true` for the first time:

    - Show generation configuration fields
    - After successful generation, automatically set `auto_generate = false`

2. **Re-activation**: When user toggles `auto_generate` to `true` again:
    - Show confirmation dialog: "This will delete all existing Davhars and Toots. Do you want to continue?"
    - If user accepts: Delete existing Davhars and Toots, then proceed with generation
    - If user cancels: Revert toggle to `false`

#### Generation Process

-   Generate Davhars and Toots for all Orcs in the Korpus
-   Follow the selected numbering type logic
-   Maintain CVSecurity integration patterns
-   Handle errors gracefully with proper validation

### Orc Level Auto-Generation

#### UI Components

-   **auto_generate**: Boolean toggle field visible on OrcEdit form
-   **Conditional Fields**: When `auto_generate = true`, the following fields become visible:
    -   `numbering_type`: Dropdown (2 = Orc-wise, 3 = Floor-wise)
    -   `number_of_floors`: Integer input for floors in this Orc
    -   `doors_per_floor`: Integer input for doors per floor
    -   `digit_multiplier`: Integer input (10, 100, 1000, etc.) - visible only for Type 3
    -   **Generate Button**: Triggers the auto-generation process

#### Behavior Logic

1. **First Time Activation**: When `auto_generate` is toggled to `true` for the first time:

    - Show generation configuration fields
    - After successful generation, automatically set `auto_generate = false`

2. **Re-activation**: When user toggles `auto_generate` to `true` again:
    - Show confirmation dialog: "This will delete all existing Davhars and Toots for this Orc. Do you want to continue?"
    - If user accepts: Delete existing Davhars and Toots for this Orc, then proceed with generation
    - If user cancels: Revert toggle to `false`

#### Generation Process

-   Generate Davhars and Toots only for the current Orc
-   Follow the selected numbering type logic (Type 2 or Type 3)
-   Independent from other Orcs in the same Korpus
-   Maintain CVSecurity integration patterns

### Davhar Level Auto-Generation

#### UI Components

-   **auto_generate**: Boolean toggle field visible on DavharEdit form
-   **Conditional Fields**: When `auto_generate = true`, the following fields become visible:
    -   `doors_per_floor`: Integer input for doors in this floor
    -   `digit_multiplier`: Integer input (10, 100, 1000, etc.)
    -   **Generate Button**: Triggers the auto-generation process

#### Behavior Logic

1. **First Time Activation**: When `auto_generate` is toggled to `true` for the first time:

    - Show generation configuration fields
    - After successful generation, automatically set `auto_generate = false`

2. **Re-activation**: When user toggles `auto_generate` to `true` again:
    - Show confirmation dialog: "This will delete all existing Toots for this floor. Do you want to continue?"
    - If user accepts: Delete existing Toots for this Davhar, then proceed with generation
    - If user cancels: Revert toggle to `false`

#### Generation Process

-   Generate Toots only for the current Davhar (floor)
-   Always uses Type 3 (Floor-wise) logic
-   Uses the floor number and digit multiplier to calculate door numbers
-   Maintain CVSecurity integration patterns

## Database Implementation

### Model Fields

#### Korpus Model

```php
class Korpus extends Model
{
    protected $fillable = [
        // Existing fields
        'numbering_type',       // 1 = Korpus-wise, 2 = Orc-wise, 3 = Floor-wise
        'digit_multiplier',     // Type 3: 10 (2-digit), 100 (3-digit), 1000 (4-digit)

        // New auto-generation fields
        'auto_generate',        // Boolean: Enable/disable auto-generation
        'number_of_floors',     // Integer: Floors per Orc for auto-generation
        'doors_per_floor',      // Integer: Doors per floor for auto-generation

        // ... other existing fields
    ];

    // Dynamic calculation methods
    public function getTotalDoorsAttribute()
    {
        return $this->orcs->sum(function ($orc) {
            return $orc->davhars->count() * $this->doors_per_floor;
        });
    }
}
```

#### Orc Model

```php
class Orc extends Model
{
    protected $fillable = [
        // New auto-generation fields
        'auto_generate',        // Boolean: Enable/disable auto-generation
        'numbering_type',       // 2 = Orc-wise, 3 = Floor-wise (for Orc-level generation)
        'number_of_floors',     // Integer: Floors in this Orc for auto-generation
        'doors_per_floor',      // Integer: Doors per floor for auto-generation
        'digit_multiplier',     // Integer: For Type 3 numbering

        // ... other existing fields
    ];

    // Dynamic calculation methods
    public function getTotalDoorsAttribute()
    {
        return $this->davhars->count() * $this->doors_per_floor;
    }
}
```

#### Davhar Model

```php
class Davhar extends Model
{
    protected $fillable = [
        // Existing fields
        'floor_number',         // Floor identifier (1, 2, 3, ...)

        // New auto-generation fields
        'auto_generate',        // Boolean: Enable/disable auto-generation
        'doors_per_floor',      // Integer: Doors in this floor for auto-generation
        'digit_multiplier',     // Integer: For floor-wise numbering

        // ... other existing fields
    ];

    // Dynamic calculation methods
    public function getTotalDoorsAttribute()
    {
        return $this->doors_per_floor;
    }
}
```

#### Toot Model

```php
class Toot extends Model
{
    protected $fillable = [
        'number',       // Door number calculated based on numbering type
        'korpus_id',    // Reference to Korpus
        'orc_id',       // Reference to Orc
        'davhar_id',    // Reference to Davhar

        // ... other existing fields
    ];
}
```

## Auto-Generation Algorithms

### Korpus Level Generation Algorithm

```php
public function generateKorpusHierarchy(Korpus $korpus)
{
    // Validate prerequisites
    if ($korpus->orcs()->count() === 0) {
        throw new Exception('Orcs must be created before auto-generation');
    }

    // Delete existing Davhars and Toots
    foreach ($korpus->orcs as $orc) {
        $orc->toots()->delete();
        $orc->davhars()->delete();
    }

    // Generate based on numbering type
    switch ($korpus->numbering_type) {
        case 1: // Korpus-wise
            $this->generateKorpusWise($korpus);
            break;
        case 2: // Orc-wise
            $this->generateOrcWise($korpus);
            break;
        case 3: // Floor-wise
            $this->generateFloorWise($korpus);
            break;
    }

    // Set auto_generate to false after generation
    $korpus->update(['auto_generate' => false]);
}
```

### Orc Level Generation Algorithm

```php
public function generateOrcHierarchy(Orc $orc)
{
    // Delete existing Davhars and Toots for this Orc
    $orc->toots()->delete();
    $orc->davhars()->delete();

    // Generate based on numbering type
    switch ($orc->numbering_type) {
        case 2: // Orc-wise
            $this->generateOrcWiseForSingleOrc($orc);
            break;
        case 3: // Floor-wise
            $this->generateFloorWiseForSingleOrc($orc);
            break;
    }

    // Set auto_generate to false after generation
    $orc->update(['auto_generate' => false]);
}
```

### Davhar Level Generation Algorithm

```php
public function generateDavharToots(Davhar $davhar)
{
    // Delete existing Toots for this Davhar
    $davhar->toots()->delete();

    // Generate Toots using floor-wise logic
    $floorNumber = $davhar->floor_number;
    $doorsPerFloor = $davhar->doors_per_floor;
    $digitMultiplier = $davhar->digit_multiplier;

    for ($i = 1; $i <= $doorsPerFloor; $i++) {
        $doorNumber = ($floorNumber * $digitMultiplier) + $i;

        Toot::create([
            'number' => $doorNumber,
            'korpus_id' => $davhar->orc->korpus_id,
            'orc_id' => $davhar->orc_id,
            'davhar_id' => $davhar->id,
            // ... other required fields
        ]);
    }

    // Set auto_generate to false after generation
    $davhar->update(['auto_generate' => false]);
}
```

## Validation Rules

### Auto-Generation Validation

#### Korpus Level Validation

1. Orcs must exist before enabling auto-generation
2. `number_of_floors` must be greater than 0
3. `doors_per_floor` must be greater than 0
4. `digit_multiplier` must be valid (10, 100, 1000, etc.) for Type 3
5. `numbering_type` must be 1, 2, or 3

#### Orc Level Validation

1. `numbering_type` must be 2 or 3 (Type 1 not supported at Orc level)
2. `number_of_floors` must be greater than 0
3. `doors_per_floor` must be greater than 0
4. `digit_multiplier` must be valid for Type 3

#### Davhar Level Validation

1. `doors_per_floor` must be greater than 0
2. `digit_multiplier` must be valid (10, 100, 1000, etc.)
3. `floor_number` must be set and greater than 0

### Existing Validation Rules

#### Type 1 Validation

1. Door numbers must be unique within a Korpus
2. Total doors calculated as: `Number of Orcs × Davhars per Orc × Doors per Davhar`
3. Door numbering must be consecutive starting from 1
4. All Orcs must have the same number of Davhars and doors per Davhar for consistency

#### Type 2 Validation

1. Door numbers must be unique within each Orc (can duplicate across Orcs)
2. Each Orc starts numbering from 1
3. Total doors per Orc calculated as: `Davhars in Orc × Doors per Davhar`
4. Door numbering must be consecutive within each Orc

#### Type 3 Validation

1. Door numbers must be unique within each Orc (can duplicate across Orcs)
2. Floor numbers must be unique within each Orc
3. Door numbers must follow the format: `(floor_number × digit_multiplier) + sequence`
4. Door sequence within a floor must start from 1 and be consecutive
5. Total doors per floor determined by `doors_per_floor` configuration

## User Interface Guidelines

### Korpus Edit Form

```php
// Auto-generation toggle
Toggle::make('auto_generate')
    ->label('Auto Generate')
    ->reactive(),

// Conditional fields (visible when auto_generate = true)
Select::make('numbering_type')
    ->label('Numbering Type')
    ->options([
        1 => 'Korpus-wise Consecutive',
        2 => 'Orc-wise Consecutive',
        3 => 'Floor-wise Consecutive'
    ])
    ->visible(fn (Get $get) => $get('auto_generate')),

TextInput::make('number_of_floors')
    ->label('Number of Floors per Orc')
    ->numeric()
    ->visible(fn (Get $get) => $get('auto_generate')),

TextInput::make('doors_per_floor')
    ->label('Doors per Floor')
    ->numeric()
    ->visible(fn (Get $get) => $get('auto_generate')),

TextInput::make('digit_multiplier')
    ->label('Digit Multiplier (10, 100, 1000)')
    ->numeric()
    ->visible(fn (Get $get) => $get('auto_generate') && $get('numbering_type') == 3),

// Generate button
Actions::make([
    Action::make('generate')
        ->label('Generate Hierarchy')
        ->action('generateHierarchy')
        ->visible(fn (Get $get) => $get('auto_generate'))
        ->requiresConfirmation()
        ->modalHeading('Generate Hierarchy')
        ->modalDescription('This will generate all Davhars and Toots based on your configuration.')
])
```

### Orc Edit Form

```php
// Similar structure but with Type 2 and 3 only
Select::make('numbering_type')
    ->label('Numbering Type')
    ->options([
        2 => 'Orc-wise Consecutive',
        3 => 'Floor-wise Consecutive'
    ])
    ->visible(fn (Get $get) => $get('auto_generate')),
```

### Davhar Edit Form

```php
// Simplified form with only doors_per_floor and digit_multiplier
TextInput::make('doors_per_floor')
    ->label('Doors in this Floor')
    ->numeric()
    ->visible(fn (Get $get) => $get('auto_generate')),

TextInput::make('digit_multiplier')
    ->label('Digit Multiplier (10, 100, 1000)')
    ->numeric()
    ->visible(fn (Get $get) => $get('auto_generate')),
```

## CVSecurity Integration

All auto-generated entities maintain full CVSecurity integration:

### Automatic Synchronization

-   **Davhars**: Sync as 'floor' entities with proper parent codes
-   **Toots**: Sync as 'door' entities with hierarchical relationships
-   **Error Handling**: Failed CVSecurity operations store null codes
-   **Consistency**: Generated entities follow same patterns as manually created ones

### Code Storage

-   Generated entities store CVSecurity response codes in 'code' fields
-   Failed synchronizations are logged and handled gracefully
-   Retry mechanisms for temporary CVSecurity service failures

## Implementation Checklist

### Database Changes

-   [ ] Add `auto_generate` boolean field to Korpus, Orc, and Davhar models
-   [ ] Add `number_of_floors` integer field to Korpus and Orc models
-   [ ] Add `doors_per_floor` integer field to Korpus, Orc, and Davhar models
-   [ ] Add `numbering_type` integer field to Orc model
-   [ ] Add `digit_multiplier` integer field to Orc and Davhar models
-   [ ] Remove `begin_toot_number` and `end_toot_number` fields from all models (replaced by dynamic calculation)
-   [ ] Create database migrations for new fields and field removals

### Backend Implementation

-   [ ] Create auto-generation service classes
-   [ ] Implement Korpus-level generation algorithms
-   [ ] Implement Orc-level generation algorithms
-   [ ] Implement Davhar-level generation algorithms
-   [ ] Add validation rules for auto-generation fields
-   [ ] Integrate with CVSecurity synchronization
-   [ ] Add error handling and logging

### Frontend Implementation

-   [ ] Update KorpusEdit form with auto-generation fields
-   [ ] Update OrcEdit form with auto-generation fields
-   [ ] Update DavharEdit form with auto-generation fields
-   [ ] Implement conditional field visibility
-   [ ] Add confirmation dialogs for destructive operations
-   [ ] Create generate buttons with proper actions
-   [ ] Add form validation and error messages

### Testing

-   [ ] Unit tests for generation algorithms
-   [ ] Integration tests with CVSecurity
-   [ ] UI tests for form interactions
-   [ ] Validation tests for edge cases
-   [ ] Performance tests for large hierarchies

## Summary

The enhanced door numbering system now provides comprehensive auto-generation capabilities at three hierarchical levels with dynamic door calculation:

1. **Korpus Level**: Complete hierarchy generation for entire building blocks
2. **Orc Level**: Targeted generation for specific entrances
3. **Davhar Level**: Granular generation for individual floors

### Key Features:

-   **Dynamic Door Calculation**: Total doors = `Number of Orcs × Davhars per Orc × Doors per Davhar`
-   **No Fixed Ranges**: Eliminates the need for predefined door number ranges
-   **Flexible Configuration**: Support for all three numbering types with dynamic calculation
-   **User-Friendly Interface**: Intuitive toggles and conditional fields
-   **Data Safety**: Confirmation dialogs for destructive operations
-   **CVSecurity Integration**: Seamless synchronization with external systems
-   **Validation**: Comprehensive validation rules and error handling
-   **Performance**: Efficient algorithms for large-scale generation

### Calculation Examples:

**Example 1**: Korpus with 2 Orcs, 16 Davhars per Orc, 6 doors per Davhar

-   Total doors = 2 × 16 × 6 = 192 doors

**Example 2**: Korpus with 3 Orcs, varying Davhars per Orc

-   Orc 1: 8 Davhars × 6 doors = 48 doors
-   Orc 2: 6 Davhars × 6 doors = 36 doors
-   Orc 3: 10 Davhars × 6 doors = 60 doors

This system provides powerful automation features that significantly reduce manual data entry while ensuring consistency and flexibility across the entire hierarchical structure through dynamic calculation rather than fixed ranges.
