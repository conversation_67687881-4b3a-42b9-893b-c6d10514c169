<?php

namespace App\Filament\Resources\Admin\KorpusResource\Pages;

use App\Filament\Resources\Admin\KorpusResource;
use App\Models\Korpus;
use App\Models\Orc;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Log;

class CreateKorpus extends CreateRecord
{
    protected static string $resource = KorpusResource::class;

    public ?string $bairId = null;

    public function mount(): void
    {
        // Try to capture the bair_id parameter when the component mounts
        $this->bairId = request()->get('bair_id') ?? request()->query('bair_id') ?? request()->input('bair_id');

        Log::info('CreateKorpus mount called', [
            'bair_id' => $this->bairId,
            'all_request_data' => request()->all(),
            'query_params' => request()->query(),
            'url' => request()->url(),
            'full_url' => request()->fullUrl()
        ]);

        parent::mount();
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        Log::info('CreateKorpus mutateFormDataBeforeCreate called', [
            'stored_bair_id' => $this->bairId,
            'input_data' => $data
        ]);

        if ($this->bairId) {
            $data[Korpus::BAIR_ID] = $this->bairId;
        }

        return parent::mutateFormDataBeforeCreate($data);
    }

    public function getBreadcrumbs(): array
    {
        $breadcrumbs = [
            url()->route('filament.admin.resources.bairs.index') => 'Байрууд',
        ];

        if ($this->bairId) {
            $breadcrumbs[url()->route('filament.admin.resources.bairs.edit', $this->bairId)] = 'Байр засах';
        }

        $breadcrumbs['#'] = 'Блок нэмэх';

        return $breadcrumbs;
    }

    protected function getRedirectUrl(): string
    {
        Log::info('CreateKorpus getRedirectUrl called', [
            'stored_bair_id' => $this->bairId,
        ]);

        if ($this->bairId) {
            $url = \App\Filament\Resources\Admin\BairResource::getUrl('edit', ['record' => $this->bairId]);
            Log::info('Redirecting to Bair edit page', ['url' => $url]);
            return $url;
        }

        $url = $this->getResource()::getUrl('edit', ['record' => $this->getRecord()]);
        Log::info('Redirecting to Korpus edit page', ['url' => $url]);
        return $url;
    }
}
