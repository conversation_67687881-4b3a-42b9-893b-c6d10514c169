<?php

namespace App\Filament\Resources\Admin\OrcResource\Pages;

use App\Filament\Resources\Admin\OrcResource;
use App\Models\Orc;
use App\Models\Davhar;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Log;

class CreateOrc extends CreateRecord
{
    protected static string $resource = OrcResource::class;

    public ?string $korpusId = null;

    public function mount(): void
    {
        $this->korpusId = request()->get('korpus_id') ?? request()->query('korpus_id') ?? request()->input('korpus_id');

        Log::info('CreateOrc mount called', [
            'korpus_id' => $this->korpusId,
            'all_request_data' => request()->all(),
        ]);

        parent::mount();
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        if ($this->korpusId) {
            $data[Orc::KORPUS_ID] = $this->korpusId;
        }

        return parent::mutateFormDataBeforeCreate($data);
    }

    public function getBreadcrumbs(): array
    {
        $breadcrumbs = [
            url()->route('filament.admin.resources.bairs.index') => 'Байрууд',
        ];

        if ($this->korpusId) {
            $korpus = \App\Models\Korpus::find($this->korpusId);
            if ($korpus) {
                $breadcrumbs[url()->route('filament.admin.resources.bairs.edit', $korpus->bair_id)] = $korpus->bair->name;
                $breadcrumbs[url()->route('filament.admin.resources.korpuses.edit', $this->korpusId)] = "Блок: {$korpus->name}";
            }
        }

        $breadcrumbs['#'] = 'Орц нэмэх';

        return $breadcrumbs;
    }

    protected function getRedirectUrl(): string
    {
        Log::info('CreateOrc getRedirectUrl called', ['stored_korpus_id' => $this->korpusId]);

        if ($this->korpusId) {
            $url = \App\Filament\Resources\Admin\KorpusResource::getUrl('edit', ['record' => $this->korpusId]);
            Log::info('Redirecting to Korpus edit page', ['url' => $url]);
            return $url;
        }

        $url = $this->getResource()::getUrl('edit', ['record' => $this->getRecord()]);
        Log::info('Redirecting to Orc edit page', ['url' => $url]);
        return $url;
    }
}
