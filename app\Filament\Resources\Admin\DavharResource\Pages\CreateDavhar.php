<?php

namespace App\Filament\Resources\Admin\DavharResource\Pages;

use App\Filament\Resources\Admin\DavharResource;
use App\Models\Davhar;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Log;

class CreateDavhar extends CreateRecord
{
    protected static string $resource = DavharResource::class;

    public ?string $orcId = null;

    public function mount(): void
    {
        $this->orcId = request()->get('orc_id') ?? request()->query('orc_id') ?? request()->input('orc_id');

        Log::info('CreateDavhar mount called', [
            'orc_id' => $this->orcId,
            'all_request_data' => request()->all(),
        ]);

        parent::mount();
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        if ($this->orcId) {
            $data[Davhar::ORC_ID] = $this->orcId;
        }

        return parent::mutateFormDataBeforeCreate($data);
    }

    public function getBreadcrumbs(): array
    {
        $breadcrumbs = [
            url()->route('filament.admin.resources.bairs.index') => 'Байрууд',
        ];

        if ($this->orcId) {
            $orc = \App\Models\Orc::find($this->orcId);
            if ($orc) {
                $breadcrumbs[url()->route('filament.admin.resources.bairs.edit', $orc->korpus->bair_id)] = $orc->korpus->bair->name;
                $breadcrumbs[url()->route('filament.admin.resources.korpuses.edit', $orc->korpus_id)] = "Блок: {$orc->korpus->name}";
                $breadcrumbs[url()->route('filament.admin.resources.orcs.edit', $this->orcId)] = "Орц: {$orc->number}";
            }
        }

        $breadcrumbs['#'] = 'Давхар нэмэх';

        return $breadcrumbs;
    }

    protected function getRedirectUrl(): string
    {
        Log::info('CreateDavhar getRedirectUrl called', ['stored_orc_id' => $this->orcId]);

        if ($this->orcId) {
            $url = \App\Filament\Resources\Admin\OrcResource::getUrl('edit', ['record' => $this->orcId]);
            Log::info('Redirecting to Orc edit page', ['url' => $url]);
            return $url;
        }

        $url = $this->getResource()::getUrl('edit', ['record' => $this->getRecord()]);
        Log::info('Redirecting to Davhar edit page', ['url' => $url]);
        return $url;
    }
}
